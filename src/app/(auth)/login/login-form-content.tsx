"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, Loader2, Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  AnimatedButton,
  AnimatedInput,
  AnimatedFormItem,
  AnimatedFormMessage,
  AnimatedFormDescription
} from "@/components/ui/animated";
import { RequiredLabel } from "@/components/ui/required-label";
import { ValidatedForm } from "@/components/forms/validated-form";
import { loginSchema } from "@/lib/validation/schemas";
import { FormField } from "@/components/ui/form";
import { AlertCircle } from "lucide-react";
import { useCallback, useRef, useState } from "react";

type LoginFormValues = {
  email: string;
  password: string;
};

export function LoginFormContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const registered = searchParams.get("registered") === "true";
  const verified = searchParams.get("verified") === "true";
  const [showPassword, setShowPassword] = useState(false);

  // Add submission guard to prevent double submissions
  const isSubmittingRef = useRef(false);
  const lastSubmissionTimeRef = useRef(0);

  const handleLogin = useCallback(async (data: LoginFormValues) => {
    console.log("handleLogin called with data:", data);

    // Validate that we have the required data FIRST (before any other checks)
    if (!data || !data.email || !data.password) {
      console.log("Login form data validation failed - ignoring submission:", data);
      // Don't update lastSubmissionTimeRef for invalid submissions
      return;
    }

    // Debounce rapid VALID submissions only (prevent valid submissions within 200ms of each other)
    const now = Date.now();
    if (now - lastSubmissionTimeRef.current < 200) {
      console.log("Valid submission debounced - too rapid");
      return;
    }
    lastSubmissionTimeRef.current = now;

    // Additional validation to ensure fields are not just empty strings
    if (data.email.trim() === "" || data.password.trim() === "") {
      console.error("Login form contains empty fields:", { email: data.email, password: "***" });
      throw new Error("Please fill in all required fields");
    }

    // Prevent double submissions (only after validation passes)
    if (isSubmittingRef.current) {
      console.log("Login already in progress, ignoring duplicate submission");
      return;
    }

    isSubmittingRef.current = true;
    try {

      console.log("Attempting login with:", { email: data.email, password: "***" });

      // Sign in with credentials
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      // Handle authentication error
      if (result?.error) {
        // Check for specific error messages from the auth provider
        if (result.error === "CredentialsSignin") {
          throw new Error("Invalid email or password");
        } else {
          throw new Error(result.error || "Authentication failed. Please try again.");
        }
      }

      // Check if user needs to complete onboarding
      const userResponse = await fetch("/api/me");

      if (!userResponse.ok) {
        // Handle non-200 responses
        const statusText = userResponse.statusText || "";
        throw new Error(`Failed to fetch user data (${userResponse.status}${statusText ? ': ' + statusText : ''}). Please try again.`);
      }

      const userData = await userResponse.json();

      // Add proper null checks
      if (userData && !userData.error) {
        // Handle different user roles and onboarding states
        if (userData.role === "BROKER") {
          // Check if broker has completed onboarding
          const brokerResponse = await fetch("/api/broker/profile");
          if (brokerResponse.ok) {
            // Broker profile exists, go to dashboard
            router.push("/broker/dashboard");
          } else {
            // Broker needs to complete onboarding
            router.push("/broker/onboarding");
          }
        } else if (userData.role === "SPV_USER") {
          // SPV users go to SPV dashboard
          router.push("/spv/dashboard");
        } else if (userData.role === "USER" || userData.role === "ORGANIZATION_USER") {
          // For regular users, check if they already belong to an organization
          if (userData.organizationId) {
            // User already belongs to an organization (created by org admin), go to dashboard
            router.push("/dashboard");
          } else {
            // User doesn't belong to an organization, check if onboarding is complete
            const onboardingResponse = await fetch("/api/onboarding");
            if (onboardingResponse.ok) {
              const onboardingData = await onboardingResponse.json();
              if (onboardingData.isComplete) {
                // Onboarding is complete, go to dashboard
                router.push("/dashboard");
              } else {
                // Onboarding is not complete, go to onboarding
                router.push("/onboarding");
              }
            } else {
              // If we can't check onboarding status, assume they need onboarding
              router.push("/onboarding");
            }
          }
        } else if (!userData.role) {
          // New users without a specific role go to onboarding
          router.push("/onboarding");
        } else {
          // Default fallback
          router.push("/dashboard");
        }

        router.refresh();
      } else {
        // Handle error in user data
        throw new Error(userData?.error || "Failed to load user data. Please try again.");
      }
    } catch (error: any) {
      // Log the error for debugging
      console.error("Login error:", error);

      // Improve error handling with better error messages
      const errorMessage = error?.message || "An unexpected error occurred. Please try again.";
      throw new Error(errorMessage);
    } finally {
      // Reset submission guard
      isSubmittingRef.current = false;
    }
  }, [router]);

  return (
    <div className="grid gap-4">
      {registered && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-700">
            Registration successful! Please check your email to verify your account.
          </AlertDescription>
        </Alert>
      )}

      {verified && (
        <Alert className="bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-700">
            Email verification successful! You can now log in.
          </AlertDescription>
        </Alert>
      )}

      <ValidatedForm
        schema={loginSchema}
        defaultValues={{ email: "", password: "" }}
        onSubmit={handleLogin}
        className="space-y-4"
        animationVariant="fadeIn"
        showErrorSummary={false}
        formOptions={{
          showToast: false,
        }}
      >
        {({ control, formState, isSubmitting, formError }) => (
          <div className="grid gap-6">
            {/* Display form error if any */}
            {formError && (
              <Alert variant="destructive" className="py-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{formError.message}</AlertDescription>
              </Alert>
            )}

            {/* Account Credentials */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-muted-foreground">Account Credentials</h3>

              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <AnimatedFormItem>
                    <RequiredLabel htmlFor="email">
                      Email
                    </RequiredLabel>
                    <AnimatedInput
                      id="email"
                      placeholder="<EMAIL>"
                      type="email"
                      autoCapitalize="none"
                      autoComplete="email"
                      autoCorrect="off"
                      disabled={isSubmitting}
                      {...field}
                    />
                    <AnimatedFormDescription className="text-xs text-muted-foreground">
                      Enter the email address associated with your account
                    </AnimatedFormDescription>
                    {formState.errors.email && (
                      <AnimatedFormMessage className="text-destructive">
                        {formState.errors.email.message as string}
                      </AnimatedFormMessage>
                    )}
                  </AnimatedFormItem>
                )}
              />

              <FormField
                control={control}
                name="password"
                render={({ field }) => (
                  <AnimatedFormItem>
                    <div className="flex items-center justify-between">
                      <RequiredLabel htmlFor="password">
                        Password
                      </RequiredLabel>
                      <Link
                        href="/forgot-password"
                        className="text-sm text-primary underline-offset-4 hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <AnimatedInput
                        id="password"
                        placeholder="********"
                        type={showPassword ? "text" : "password"}
                        autoComplete="current-password"
                        disabled={isSubmitting}
                        className="pr-10"
                        {...field}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                    <AnimatedFormDescription className="text-xs text-muted-foreground">
                      Enter your password to access your account
                    </AnimatedFormDescription>
                    {formState.errors.password && (
                      <AnimatedFormMessage className="text-destructive">
                        {formState.errors.password.message as string}
                      </AnimatedFormMessage>
                    )}
                  </AnimatedFormItem>
                )}
              />
            </div>

            <AnimatedButton
              type="submit"
              className="w-full mt-2"
              disabled={isSubmitting}
              animationVariant="buttonTap"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                "Sign In"
              )}
            </AnimatedButton>
          </div>
        )}
      </ValidatedForm>
    </div>
  );
}
