#!/bin/bash

echo "🧪 Testing User Creation API Fixes..."
echo ""

# Test 1: Check if roles endpoint returns system roles correctly
echo "1. Testing roles endpoint..."
curl -s -X GET "http://localhost:3001/api/rbac/roles" \
  -H "Content-Type: application/json" | jq '.' > roles_response.json

if [ $? -eq 0 ]; then
  echo "✅ Roles endpoint accessible"
  echo "📊 System roles found:"
  cat roles_response.json | jq '.data[] | select(.isSystemRole == true) | {id, name, displayName}'
else
  echo "❌ Roles endpoint failed"
  exit 1
fi

echo ""
echo "2. Testing user creation with system role..."

# Extract a system role ID (USER role)
USER_ROLE_ID=$(cat roles_response.json | jq -r '.data[] | select(.name == "USER" and .isSystemRole == true) | .id')

if [ "$USER_ROLE_ID" = "null" ] || [ -z "$USER_ROLE_ID" ]; then
  echo "❌ Could not find USER system role ID"
  exit 1
fi

echo "📋 Using USER role ID: $USER_ROLE_ID"

# Test user creation
curl -s -X POST "http://localhost:3001/api/rbac/users" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Test User\",
    \"email\": \"<EMAIL>\",
    \"password\": \"password123\",
    \"jobTitle\": \"Test Job\",
    \"departmentName\": \"Test Department\",
    \"phoneNumber\": \"+1234567890\",
    \"roleIds\": [\"$USER_ROLE_ID\"]
  }" > user_creation_response.json

echo "📊 User creation response:"
cat user_creation_response.json | jq '.'

# Check if successful
if cat user_creation_response.json | jq -e '.success' > /dev/null; then
  echo "✅ User creation successful!"
else
  echo "❌ User creation failed"
  echo "🔍 Error details:"
  cat user_creation_response.json | jq '.error'
fi

echo ""
echo "🧹 Cleaning up test files..."
rm -f roles_response.json user_creation_response.json
