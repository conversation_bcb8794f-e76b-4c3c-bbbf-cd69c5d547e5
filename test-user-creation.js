#!/usr/bin/env node

/**
 * Test script to verify user creation with system roles
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3001';

async function testUserCreation() {
  console.log('🧪 Testing User Creation with System Roles...\n');

  try {
    // First, let's test the roles endpoint to see if system roles are returned correctly
    console.log('1. Testing roles endpoint...');
    const rolesResponse = await fetch(`${BASE_URL}/api/rbac/roles`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!rolesResponse.ok) {
      console.log('❌ Roles endpoint failed:', rolesResponse.status, rolesResponse.statusText);
      const errorText = await rolesResponse.text();
      console.log('Error details:', errorText);
      return;
    }

    const rolesData = await rolesResponse.json();
    console.log('✅ Roles endpoint successful');
    console.log('📊 Roles data:', JSON.stringify(rolesData, null, 2));

    // Find a system role ID to test with
    const systemRoles = rolesData.data?.filter(role => role.isSystemRole) || [];
    console.log('\n📋 System roles found:', systemRoles.map(r => ({ id: r.id, name: r.name, displayName: r.displayName })));

    if (systemRoles.length === 0) {
      console.log('❌ No system roles found in the response');
      return;
    }

    // Test user creation with a system role
    const userRole = systemRoles.find(r => r.name === 'USER');
    if (!userRole) {
      console.log('❌ USER system role not found');
      return;
    }

    console.log(`\n2. Testing user creation with system role: ${userRole.name} (ID: ${userRole.id})`);
    
    const testUserData = {
      name: "Test User",
      email: "<EMAIL>",
      password: "password123",
      jobTitle: "Test Job",
      departmentName: "Test Department",
      phoneNumber: "+1234567890",
      roleIds: [userRole.id]
    };

    const createUserResponse = await fetch(`${BASE_URL}/api/rbac/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUserData),
    });

    console.log('📤 User creation request sent');
    console.log('📊 Response status:', createUserResponse.status);

    const responseText = await createUserResponse.text();
    console.log('📊 Response body:', responseText);

    if (createUserResponse.ok) {
      console.log('✅ User creation successful!');
    } else {
      console.log('❌ User creation failed');
      
      // Try to parse the error
      try {
        const errorData = JSON.parse(responseText);
        console.log('🔍 Error details:', errorData);
      } catch (e) {
        console.log('🔍 Raw error response:', responseText);
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testUserCreation();
